#!/usr/bin/env python3

import requests
import json

# Server URL
BASE_URL = "http://localhost:5001"

def main():
    # Create a new session
    session = requests.Session()
    
    # Get initial page to establish session
    response = session.get(BASE_URL + "/")
    print("Initial response status:", response.status_code)
    
    # Keep pulling until we get 50 babas or run out of wishes
    babas = 0
    wishes = 50
    pulls = 0
    
    while babas < 50 and wishes > 0:
        response = session.post(BASE_URL + "/api/pull")
        pulls += 1
        
        if response.status_code == 200:
            data = response.json()
            babas = data.get('babas', 0)
            wishes = data.get('wishes', 0)
            baba_gotten = data.get('babaGotten', False)
            
            print(f"Pull {pulls}: {'Baba!' if baba_gotten else 'No baba'} - Total babas: {babas}, Wishes left: {wishes}")
            
            if babas >= 50:
                print("🎉 WE DID IT! Flag:", data.get('babaGotten'))
                return
        else:
            print(f"Pull {pulls} failed: {response.status_code} - {response.text}")
            break
    
    print(f"Finished with {babas} babas and {wishes} wishes left")
    if babas < 50:
        print("Need to find another way to get more babas...")

if __name__ == "__main__":
    main()
