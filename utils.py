import sqlite3
import hashlib
import jwt
import os
JWT_SECRET =  os.urandom(32).hex()
SALT = os.urandom(16).hex()
def generate_jwt(user_id, wishes = 50, babas = 0):
    payload = {
        'user_id': user_id,
        'wishes': wishes,
        'babas': babas
    }
    return jwt.encode(payload, JWT_SECRET, algorithm='HS256')

def connect(db_file):
    conn = sqlite3.connect(db_file)
    conn.row_factory = sqlite3.Row
    return conn

def init_db():
    conn = sqlite3.connect('database.db')
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS tokensUsed (
            hash TEXT NOT NULL,
            uuid TEXT NOT NULL,
            PRIMARY KEY (uuid, hash)
        )
    ''')
    conn.commit()
    conn.close()

def generate_hash(token):
    """
    Generates a hash for the given token using SHA256 and a salt.
    """
    return hashlib.sha256((SALT+token).encode("latin-1")).hexdigest()

def insert_token_used(user_id,token):
    hash_value = generate_hash(token)
    insert_hash(user_id, hash_value)
    return hash_value

def insert_hash(user_id, hash):
    with connect('database.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            INSERT OR IGNORE INTO tokensUsed (uuid, hash) VALUES (?, ?)
        ''', (user_id, hash))
        conn.commit()

def check_hash(hash, jwtCookie):
    hash_value = generate_hash(jwtCookie)
    return hash == hash_value


def check_token_used(user_id, token):
    print("Checking if token is used for user:", user_id, "with token:", token)
    hash_value = generate_hash(token)
    print("Generated hash value:", hash_value)
    with connect('database.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM tokensUsed WHERE uuid = ? AND hash = ?
        ''', (user_id, hash_value))
        result = cursor.fetchone()
    return result is not None

def check_hash_used(user_id,hash):
    with connect('database.db') as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT * FROM tokensUsed WHERE uuid = ? AND hash = ?
        ''', (user_id, hash))
        result = cursor.fetchone()
    return result is not None