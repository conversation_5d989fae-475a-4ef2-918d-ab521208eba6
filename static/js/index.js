 document.querySelector('.pull-button').addEventListener('click', async function() {
            // Logic for pulling a character or weapon
            const response = await fetch('/api/pull', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
            })
            const data = await response.json();
            if (data.success) {
                // Update the UI with the new character or weapon
                document.querySelector('.baba-count').innerText = data.babas;
                document.querySelector('.wish-count').innerText = data.wishes;
                if (data.babaGotten) {
                    alert(`You pulled a Baba!`);
                }
                else{
                    alert(`You pulled a no Baba :c`);
                }
            } else {
                alert(data.error);
            }
        });