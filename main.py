from flask import Flask, request, jsonify, render_template,session, g
import os
import uuid
import jwt 
import random
from utils import generate_jwt, check_token_used, check_hash, check_hash_used, insert_token_used, init_db, JWT_SECRET, generate_hash

BABAS_REQUIRED = 50  # Number of babas required to win
app = Flask(__name__)
app.secret_key = os.urandom(32).hex()
FLAG = os.getenv('FLAG', 'LNC25{example_flag}')

def pull_rng(wishes, babas, user_id, old_jwt):
    insert_token_used(user_id, old_jwt)
    babaGotten = random.random() < 0.5
    if babaGotten:
        babas += 1
    wishes -= 1
    jwt_token = generate_jwt(user_id, wishes, babas)
    return wishes, babas, babaGotten, jwt_token

@app.before_request
def before_request():
    if 'uuid' not in session:
        session['uuid'] = str(uuid.uuid4())
    cookies = request.cookies
    if "jwt" not in cookies:
        print("No JWT found in cookies, generating new one")
        g.jwt = generate_jwt(session['uuid'],BABAS_REQUIRED, 0)
    else:
        g.jwt = cookies.get("jwt")

@app.after_request
def after_request(response):
    print("updating", g.jwt)
    response.set_cookie('jwt', g.jwt, httponly=True)
    return response

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/pull', methods=['POST'])
def pull():
    user_id = session.get('uuid')
    jwt_token = g.jwt
    if check_token_used(user_id, jwt_token):
        return jsonify({'error': 'Token already used'}), 403
    try:
        data = jwt.decode(jwt_token, JWT_SECRET, algorithms=['HS256'])
    except jwt.InvalidTokenError:
        g.jwt = generate_jwt(user_id, BABAS_REQUIRED, 0)
        return jsonify({'error': 'Invalid JWT'}), 403

    JWT_user_id, wishes, babas = data.get('user_id',None), data.get('wishes', 0), data.get('babas', 0)
    if JWT_user_id != user_id:
        g.jwt = generate_jwt(user_id, BABAS_REQUIRED, 0)
        return jsonify({'error': 'JWT does not match session user ID'}), 403
    
    if wishes <= 0:
        return jsonify({'error': 'No wishes left'}), 403
    
    sanitised_jwt = generate_jwt(user_id, wishes, babas)
    wishes, babas, babaGotten, jwt_token = pull_rng(wishes, babas, user_id, sanitised_jwt)
    if babas >= BABAS_REQUIRED:
        return jsonify({'success': True, 'wishes': wishes, 'babas': babas, 'babaGotten': FLAG})
    g.jwt = jwt_token
    return jsonify({'success': True, 'wishes': wishes, 'babas': babas, 'babaGotten': babaGotten})

@app.route('/api/developer', methods=['POST'])
def developer():
    # Allows to insert jwt and hashes directly
    # Hash and JWT should match 
    data = request.json
    user_id = session.get('uuid')
    if not data or 'hash' not in data or 'jwt' not in data:
        return jsonify({'error': 'Invalid input'}), 400
    
    try:
        JWT_data = jwt.decode(data['jwt'], JWT_SECRET, algorithms=['HS256'])
        JWT_user_id, wishes, babas = JWT_data.get('user_id',None), JWT_data.get('wishes', 0), JWT_data.get('babas', 0)
    except jwt.InvalidTokenError:
        g.jwt = generate_jwt(user_id, BABAS_REQUIRED, 0)
        return jsonify({'error': 'Invalid JWT'}), 403
    
    if JWT_user_id != user_id:
        return jsonify({'error': 'JWT does not match session user ID'}), 403
    
    if not check_hash(data['hash'], data['jwt']):
        # Recreate JWT so everytime the same data is given it will always use the same JWT
        cleaned_JWT = generate_jwt(user_id, wishes, babas)
        hash_value = generate_hash(cleaned_JWT)
        return jsonify({'error': 'Hash does not match JWT', 'expected': hash_value}), 400
    
    if check_hash_used(user_id, data['hash']):
        return jsonify({'error': 'Hash already used'}), 403
    
    if wishes <= 0:
        return jsonify({'error': 'No wishes left'}), 403
    
    wishes, babas, babaGotten, jwt_token = pull_rng(wishes, babas, user_id, data['jwt'])
    if babas >= BABAS_REQUIRED:
        return jsonify({'success': True, 'wishes': wishes, 'babas': babas, 'babaGotten': FLAG})
    g.jwt = jwt_token
    return jsonify({
        'success': True,
        'wishes': wishes,
        'babas': babas,
        'babaGotten': babaGotten,
        'jwt': jwt_token
    })
    
if __name__ == '__main__':
    if not os.path.exists('database.db'):
        init_db()
    app.run(port=5001, host='0.0.0.0')