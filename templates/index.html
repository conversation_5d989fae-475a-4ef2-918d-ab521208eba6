<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Jenshin Impact</title>
    <script src="https://cdn.jsdelivr.net/npm/@tailwindcss/browser@4"></script>
</head>

<body>
    <img src="{{ url_for('static', filename='images/bg.png') }}" alt="Jenshin Impact"
        class="w-screen h-screen absolute top-0 left-0 object-cover -z-1 hue-rotate-90 brightness-80" >
    <nav class = "flex justify-between px-12 py-12">
        <div class = "flex items-center gap-4">
            <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo"
                class="h-12">
        </div>
        <div class = "flex items-center gap-12 ">
            <div class = "flex items-center gap-4 bg-white/50 rounded-full px-3 py-1 shadow-[0_0_6px_rgba(0,0,0,0.7)]">
                <img src ="{{ url_for('static', filename='images/baba.png') }}" alt="Character"
                    class="w-6 h-6">
                <p class = "text-xl text-shadow-lg font-bold text-gray-700 baba-count">0</p>
            </div>
            <div class = "flex items-center gap-4 bg-white/50 rounded-full px-3 py-1 shadow-[0_0_6px_rgba(0,0,0,0.7)]">
                <img src="{{ url_for('static', filename='images/pull_logo.png') }}" alt="Weapon"
                    class="w-6 h-6">
                <p class = "text-xl text-shadow-lg font-bold text-gray-700 wish-count">50</p>
            </div>
        </div>
    </nav>
    <div class = "flex flex-col items-center justify-center absolute top-9/20 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-4/7 shadow-2xl">
            <img src="{{ url_for('static', filename='images/banner.png') }}" alt="Character"
                class="bg-white w-full h-full ">


    </div>
    <div class = "absolute bottom-12 right-12">
        <div class = "pull-button flex flex-col items-center gap-2 bg-white rounded-full px-32 py-1 shadow-[0_0_6px_rgba(0,0,0,0.7)] border-[#C0C0C0] border-3 hover:brightness-90 cursor-pointer transition-all duration-300">
            <p class = "font-bold text-xl text-gray-500">Wish x1</p>
            <div class="flex items-center gap-2 flex-row">
                <img src="{{ url_for('static', filename='images/pull_logo.png') }}" alt="Pull"
                    class="w-6 h-6">
                <p>x</p>
                <p class = "text-xl text-shadow-lg font-bold text-gray-900">1</p>
            </div>
        </div>
    </div>
    <script src = "{{ url_for('static', filename='js/index.js') }}"></script>
</body>

</html>